package com.scash.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: TD_ZHX
 * @Date: 2021/5/21 15:02
 * @Description: 写点注释吧！！
 */
@Data
@ApiModel(value = "OrderQueryDto", description = "订单查询的条件对象")
public class OrderQuery {

    @ApiModelProperty(value = "店铺名称", position = 1)
    private String storeName;

    @ApiModelProperty(value = "店铺ID", position = 1)
    private Long storeId;

    @ApiModelProperty(value = "订单号", position = 2)
    private String orderNo;

    @ApiModelProperty(value = "订单类型", position = 3)
    private String orderType;

    @ApiModelProperty(value = "订单状态： 1-pend_paid（待支付）  2-pend_deliver（待发货） 3-pend_pick_up（待提货）\n" +
            "                     4-pend_receive（已发货）  5-canceled（已取消）  6-completed（已完成）  7-refunded（已退款）", position = 4)
    private String orderStatus;

    @ApiModelProperty(value = "status", position = 5)
    private List<Integer> status;

    @ApiModelProperty(value = "商品名称", position = 5)
    private String goodName;

    @ApiModelProperty(value = "商品套餐名", position = 6)
    private String goodSku;

    @ApiModelProperty(value = "用户ID", position = 7, hidden = true)
    private Long userId;
}
