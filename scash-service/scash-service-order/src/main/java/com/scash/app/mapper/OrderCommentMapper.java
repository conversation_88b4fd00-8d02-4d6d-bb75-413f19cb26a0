package com.scash.app.mapper;


import com.scash.app.domain.OrderComment;
import com.scash.app.domain.dto.GoodsCommentDto;
import com.scash.app.domain.dto.GoodsRatingDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品评价表
 */
public interface OrderCommentMapper {

    GoodsRatingDto scoreAggregation(@Param("goodsId") Long goodsId, @Param("storeId") Long storeId);

    void saveBatch(@Param("orderCommentList") List<OrderComment> orderCommentList);

    List<OrderComment> selectOrderComment(@Param("goodsId") Long merchantGoodId, @Param("storeId") Long storeId, @Param("score") Integer score);

    List<GoodsCommentDto> selectOrderComments(@Param("goodsId") Long merchantGoodId, @Param("storeId") Long storeId, @Param("score") Integer score);
}
