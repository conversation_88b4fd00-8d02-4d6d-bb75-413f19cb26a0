package com.scash.app.mapper;


import com.scash.app.domain.OrderGood;
import com.scash.app.domain.dto.OrderBuyNowDto;
import com.scash.app.domain.dto.OrderPayDto;
import com.scash.app.domain.dto.OrderQueryDto;
import com.scash.app.domain.dto.OrderQueryVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单商品Mapper接口
 * <AUTHOR>
 * @date 2021-03-23
 */
public interface OrderGoodMapper {


    /**
     * 查询订单商品列表
     */
    List<OrderGood> selectOrderGoodList(@Param("orderId") Long orderId,@Param("mainOrderId") Long mainOrderId);

    /**
     * 查询订单商品列表
     */
    List<OrderGood> selectH5OrderGoodList(@Param("orderId") Long orderId,@Param("mainOrderId") Long mainOrderId);

    /**
     * 新增订单商品
     */
    int insertBatchOrderGood(@Param("orderGoods") List<OrderGood> orderGoods);

    /**
     * 获取当天销量排行榜商品
     */
    List<Long> getHoySalesTopGoods(@Param("merchantId") Long merchantId, @Param("storeId") Long storeId, @Param("topNum") Integer topNum);

    /**
     * 获取当天分组消费额
     */
    BigDecimal getHoyGroupGoodInfoSales(@Param("merchantId") Long merchantId, @Param("storeId") Long storeId, @Param("goodIds") List<Long> goodIds);


    /**
     * 查询出用户的订单列表
     */
    List<OrderQueryVo> selectCustomerOrderList(OrderQueryDto orderQueryDto);

    /**
     * 删除订单
     */
    Integer deleteOrderByOrderIds(@Param("orderIds") List<String> orderIds, @Param("userId") Long userId);

    /**
     * 通过订单号查询出订单信息
     */
    OrderQueryVo selectCustomerOrderInfo(OrderPayDto orderPayDto);


    /**
     * 通过购物车查询出订单商品的数据信息
     */
    List<OrderGood> selectGoodsInfoByCartIdList(@Param("cartId") List<Long> cartId, @Param("userId") Long userId);

    /**
     * 通过订单商品Id 和 skuID 查询出数据
     */
    List<OrderGood> orderBuyNow(OrderBuyNowDto orderBuyNowDto);

    /**
     * 通过订单商品Id 和 skuID 查询出数据
     */
    List<OrderGood> selectGoodsByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 获取订单商品信息
     */
    OrderGood getOrderGoodInfo(@Param("orderId") Long orderId,@Param("goodId")Long goodId);
}
