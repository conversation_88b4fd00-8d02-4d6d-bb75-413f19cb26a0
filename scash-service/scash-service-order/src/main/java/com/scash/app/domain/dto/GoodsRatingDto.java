package com.scash.app.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商品评分
 */
@Data
@ApiModel(value = "GoodsRatingDto", discriminator = "商品评分数量")
public class GoodsRatingDto {


    @ApiModelProperty(value = "好评率")
    private String favorableRate;

    @ApiModelProperty(value = "全部")
    private Integer all;

    @ApiModelProperty(value = "五星")
    private Integer fiveStars;

    @ApiModelProperty(value = "四星")
    private Integer fourStars;

    @ApiModelProperty(value = "三星")
    private Integer threeStars;

    @ApiModelProperty(value = "二星")
    private Integer twoStars;

    @ApiModelProperty(value = "一星")
    private Integer oneStars;

}
