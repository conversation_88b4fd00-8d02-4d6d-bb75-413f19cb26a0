package com.scash.app.domain;


import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;

/**
 * <AUTHOR>
 * @ClassName MyAuthenticator
 * @Desc 说明：发送邮件需要使用的基本信息
 * @Date 2021/3/19 9:30
 * @Version
 **/
public class MyAuthenticator extends Authenticator {

    String userName = null;
    String password = null;

    public MyAuthenticator() {
    }

    public MyAuthenticator(String username, String password) {
        this.userName = username;
        this.password = password;
    }

    @Override
    protected PasswordAuthentication getPasswordAuthentication() {
        return new PasswordAuthentication(userName, password);
    }
}
