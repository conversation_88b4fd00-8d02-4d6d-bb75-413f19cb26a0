<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.scash</groupId>
        <artifactId>scash-service</artifactId>
        <version>2.5.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>scash-service-storage</artifactId>

    <description>
        scash-service-storage存储对象服务类
    </description>

    <dependencies>
		
        <!-- Common Core-->
        <dependency>
            <groupId>com.scash</groupId>
            <artifactId>scash-common-core</artifactId>
        </dependency>

        <!-- <PERSON>yun Oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.5.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Tencent COS SDK-->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.133</version>
        </dependency>


    </dependencies>

</project>