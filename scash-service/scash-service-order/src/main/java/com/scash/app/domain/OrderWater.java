package com.scash.app.domain;

import com.scash.common.core.annotation.Excel;
import com.scash.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单流水对象 scash_order_water
 * <AUTHOR>
 * @date 2021-05-12
 */
@Data
@ApiModel("订单流水")
public class OrderWater extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    @ApiModelProperty("订单号")
    private String orderNo;

    /**
     * 资金流水号
     */
    @Excel(name = "Transaction No")
    @ApiModelProperty("资金流水号")
    private String paymentNo;

    /**
     * 交易流水号
     */
    @Excel(name = "Partner Transaction No")
    @ApiModelProperty("交易流水号")
    private String tradeNo;

    /**
     * 订单Id
     */
    @ApiModelProperty("订单Id")
    private Long orderId;

    /**
     * 商户Id
     */
    @ApiModelProperty("商户Id")
    private Long merchantId;

    /**
     * 店铺Id
     */
    @ApiModelProperty("店铺Id")
    private Long storeId;

    /**
     * 公司名称
     */
    @Excel(name = "Company Name")
    @ApiModelProperty("公司名称")
    private String companyName;

    /**
     * 公司No
     */
    @ApiModelProperty("公司NO")
    private String merchantNo;

    /**
     * 店铺No
     */
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty("Store Name")
    @Excel(name = "Store Name")
    private String storeName;

    /**
     * 流水类型：1-收款 2-退款
     */
    @Excel(name = "流水类型：1-收款 2-退款")
    @ApiModelProperty("流水类型：1-收款 2-退款")
    private Integer waterType;

    /**
     * 支付通道
     */
    @ApiModelProperty("支付通道")
    private String paymentChannel;

    /**
     * 支付通道名称
     */
    @Excel(name = "支付通道名称")
    @ApiModelProperty("支付通道名称")
    private String paymentChannelName;

    /**
     * 支付类型
     */
    @Excel(name = "支付类型")
    @ApiModelProperty("支付类型")
    private String paymentType;

    /**
     * 实收金额
     */
    @Excel(name = "实收金额")
    @ApiModelProperty("实收金额")
    private BigDecimal actualPrice;

    /**
     * 仅用于APP-流水合计
     */
    private BigDecimal totalPrice = BigDecimal.ZERO;

    /**
     * 订单币种
     */
    @ApiModelProperty("订单币种")
    private String currency;

    /**
     * 顾客名称
     */
    private String customerName;

    @ApiModelProperty("订单类型：online-线上  offline-线下")
    @Excel(name = "Order Type", readConverterExp = "online=Online Order,offline=Offline Order")
    private String orderSource;

    /**
     * 订单来源：0-内部  1-外部
     */
    @Excel(name = "Order Relation", readConverterExp = "0=Inside,1=OutSide")
    @ApiModelProperty("订单来源：0-内部  1-外部")
    private Integer orderRelation;

    /**
     * 子订单Id
     */
    private Long subOrderId;

    /**
     * 子订单号
     */
    private String subOrderNo;

    /**
     * 运费
     */
    private BigDecimal postage;

    /**
     * 核销标记:0-已核销  1-未核销
     */
    private Integer chargeOff;

    /**
     * 核销记录Id
     */
    private Long chargeOffId;


    @ApiModelProperty("支付通道")
    private List<String> paymentChannels;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("orderNo", getOrderNo())
                .append("orderId", getOrderId())
                .append("merchantId", getMerchantId())
                .append("storeId", getStoreId())
                .append("waterType", getWaterType())
                .append("paymentChannel", getPaymentChannel())
                .append("paymentChannelName", getPaymentChannelName())
                .append("paymentType", getPaymentType())
                .append("actualPrice", getActualPrice())
                .append("createTime", getCreateTime())
                .toString();
    }
}
