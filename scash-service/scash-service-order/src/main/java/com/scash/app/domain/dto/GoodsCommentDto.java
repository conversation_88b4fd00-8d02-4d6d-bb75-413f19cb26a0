package com.scash.app.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品评论
 */
@Data
@ApiModel(value = "GoodsCommentDto", discriminator = "商品评论")
public class GoodsCommentDto {

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "评分")
    private Integer score;

    @ApiModelProperty(value = "商品sku")
    private String[] sku;

    @ApiModelProperty(value = "评论内容")
    private String evaluationContent;

    @ApiModelProperty(value = "评论图片 ")
    private String[] evaluationPicture;

    @ApiModelProperty(value = "评论时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime commentTime;

}
