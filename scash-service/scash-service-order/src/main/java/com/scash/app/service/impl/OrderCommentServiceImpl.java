package com.scash.app.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSONObject;
import com.scash.app.domain.Customer;
import com.scash.app.domain.Order;
import com.scash.app.domain.OrderComment;
import com.scash.app.domain.OrderGood;
import com.scash.app.domain.dto.GoodsCommentDto;
import com.scash.app.domain.dto.GoodsRatingDto;
import com.scash.app.domain.dto.OrderCommentDto;
import com.scash.app.domain.dto.StoreGoodInfoDto;
import com.scash.app.mapper.*;
import com.scash.app.service.IOrderCommentService;
import com.scash.common.core.constant.CacheConstants;
import com.scash.common.core.exception.BaseException;
import com.scash.common.core.utils.OrderUtils;
import com.scash.common.core.utils.SecurityUtils;
import com.scash.common.core.utils.StringUtils;
import com.scash.common.core.utils.bean.BeanUtils;
import com.scash.common.core.web.domain.AjaxResult;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class OrderCommentServiceImpl implements IOrderCommentService {

    /**
     * 限制评价时间
     */
    private final Integer EVALUATION_TIME = 16;

    @Autowired
    private OrderCommentMapper orderCommentMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderGoodMapper orderGoodMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private StoreGoodInfoMapper storeGoodInfoMapper;
    @Resource
    private MerchantGoodInfoMapper merchantGoodInfoMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    @Transactional(rollbackFor = BaseException.class)
    public AjaxResult save(List<OrderCommentDto> req) {
        //获取用户Id
        String token = SecurityUtils.getToken();
        Long userId = null;
        if (ObjectUtils.isNotEmpty(token)) {
            String userStr = stringRedisTemplate.opsForValue().get(CacheConstants.LOGIN_TOKEN_KEY + token);
            if (StringUtils.isNull(userStr)) {
                throw new BaseException("token does not exist");
            }
            JSONObject obj = JSONObject.parseObject(userStr);
            userId = Long.parseLong(obj.getString("userid"));
        }
        //获取订单号
        List<Long> orderIds = req.stream().map(OrderCommentDto::getOrderId).collect(Collectors.toList());
        //所有订单
        List<Order> orderList = orderMapper.selectOrderByIds(orderIds);
        List<OrderGood> orderGoods = orderGoodMapper.selectGoodsByOrderIds(orderIds);
        Map<Long, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));

        Long finalUserId = userId;
        List<OrderComment> orderCommentList = req.stream().filter(orderCommentDto -> {
            //订单状态为401/402、且未评论过的订单才可以评论
            Order order = orderMap.get(orderCommentDto.getOrderId());
            if (!order.getOrderSource().equals(OrderUtils.ORDRE_SOURCE_ONLINE)) {
                throw new BaseException("Orders are not allowed to be evaluated");
            }
            if (!order.getOrderStatus().equals(OrderUtils.COMPLETED_U)
                    && !order.getOrderStatus().equals(OrderUtils.COMPLETED_S)) {
                throw new BaseException("Orders are not allowed to be evaluated");
            }
            orderCommentDto.setStoreId(order.getStoreId());
            OrderGood orderGood = orderGoodMapper.getOrderGoodInfo(order.getId(), orderCommentDto.getSkuId());
            if (orderGood != null) {
                orderCommentDto.setGoodSku(orderGood.getGoodSku());
            }
            return true;
        }).map(orderCommentDto -> {
            OrderComment orderComment = new OrderComment();
            BeanUtils.copyProperties(orderCommentDto, orderComment);
            Customer customer = customerMapper.selectCustomerById(finalUserId);
            if (customer != null) {
                orderComment.setUserId(customer.getUserId());
                orderComment.setAvatar(customer.getAvatar());
                orderComment.setNickName(customer.getNickName());
            }
            return orderComment;
        }).collect(Collectors.toList());
        if (orderCommentList.size() == 0) {
            return AjaxResult.error();
        }
        //保存评论
        orderCommentMapper.saveBatch(orderCommentList);
        //修改订单为已评价
        orderMapper.increaseNumberComments(orderIds);
        //商品表评论数量增加
        for (OrderGood orderGood : orderGoods) {
            Order order = orderMap.get(orderGood.getOrderId());
            if (order != null) {
                storeGoodInfoMapper.increaseNumberComments(order.getStoreId(), orderGood.getGoodId());
                merchantGoodInfoMapper.addGoodsComment(orderGood.getGoodId());
            }
        }
        return AjaxResult.success();
    }

    @Override
    public List<GoodsCommentDto> viewComments(Long goodsId,Long storeId ,Integer score) {
        //GoodsCommentDto
        /*List<OrderComment> orderCommentList = orderCommentMapper.selectOrderComment(goodsId, storeId, score);
        if (orderCommentList.size() == 0) {
            return new ArrayList<>();
        }
        //评论
        List<GoodsCommentDto> collect = orderCommentList.stream().map(orderComment -> {
            GoodsCommentDto goodsCommentDto = new GoodsCommentDto();
            BeanUtils.copyBeanProp(goodsCommentDto, orderComment);
            goodsCommentDto.setCommentTime(orderComment.getCreateTime());
            goodsCommentDto.setSku(orderComment.getGoodSku());
            goodsCommentDto.setScore((orderComment.getProductQualityScore() + orderComment.getMerchantServicePoints()) / 2);
            return goodsCommentDto;
        }).collect(Collectors.toList());
        return collect;*/
        return orderCommentMapper.selectOrderComments(goodsId,storeId,score);
    }

    @Override
    public GoodsRatingDto viewRating(Long goodsId) {
        //获取M商品Id，以及店铺Id
        StoreGoodInfoDto storeGoodInfoDto = storeGoodInfoMapper.selectStoreGoodInfoById(goodsId);
        if (storeGoodInfoDto == null) {
            throw new BaseException("The goods are invalid");
        }
        GoodsRatingDto goodsRatingDto = orderCommentMapper.scoreAggregation(storeGoodInfoDto.getMerchantGoodId(), storeGoodInfoDto.getStoreId());
        if (goodsRatingDto != null) {
            Double db = Double.valueOf(goodsRatingDto.getFiveStars() + goodsRatingDto.getFourStars()) / Double.valueOf(goodsRatingDto.getAll());
            boolean validNumber = NumberUtil.isValidNumber(db);
            if (!validNumber) {
                db = 1.0;
            }
            goodsRatingDto.setFavorableRate(NumberUtil.formatPercent(db, 3));
            return goodsRatingDto;
        }
        return null;
    }
}
