package com.scash.app.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 订单评价dto
 */
@Data
@ApiModel(value = "OrderCommentDto", discriminator = "评价参数")
public class OrderCommentDto {

    @ApiModelProperty(value = "订单id")
    @NotNull(message = "orderId cannot be empty")
    private Long orderId;

    @ApiModelProperty(value = "店铺Id")
    private Long storeId;

    @ApiModelProperty(value = "商品Id")
    @NotNull(message = "goodId cannot be empty")
    private Long goodsId;

    @ApiModelProperty(value = "SKU_Id")
    @NotNull(message = "skuId cannot be empty")
    private Long skuId;

    /**
     * 商品SKU信息
     */
    @ApiModelProperty(value = "商品SKU信息")
    private String[] goodSku;

    @ApiModelProperty(value = "产品质量分")
    @NotNull(message = "productQualityScore cannot be empty")
    private Integer productQualityScore;

    @ApiModelProperty(value = "商家服务分")
    @NotNull(message = "merchantServicePoints cannot be empty")
    private Integer merchantServicePoints;

    @ApiModelProperty(value = "评价内容")
    @NotNull(message = "evaluationContent cannot be empty")
    private String evaluationContent;

    @ApiModelProperty(value = "评价图片")
    @NotNull(message = "evaluationPicture cannot be empty")
    private String[] evaluationPicture;

}
