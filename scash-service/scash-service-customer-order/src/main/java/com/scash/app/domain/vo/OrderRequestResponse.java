package com.scash.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单请求响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderRequestResponse {
    @ApiModelProperty(value = "分页Token")
    private String pageToken;

    @ApiModelProperty(value = "优惠券ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long couponId;

    @ApiModelProperty("费用明细对象")
    private OrderFeeDTO feeDetails;

    @ApiModelProperty("店铺订单信息")
    private StoreOrderDTO storeOrderDTO;

    @ApiModelProperty("我的优惠券列表")
    private List<CouponVO> availableCoupons;

    /**
     * 按店铺组织的订单信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StoreOrderDTO {
        /**
         * 店铺ID
         */
        private Long storeId;

        /**
         * 店铺logo
         */
        private String storeLogo;

        /**
         * 店铺名称
         */
        private String storeName;
        /**
         * 该店铺下的所有商品列表
         */
        private List<ProductDTO> products;

    }

    /**
     * 结算的商品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductDTO {

        @ApiModelProperty(name = "商品ID")
        private Long goodId;

        @ApiModelProperty("是否有SKU  0-有  1-没有")
        private Integer skuFlag;

        @ApiModelProperty(name = "商品套餐ID")
        private Long skuId;

        @ApiModelProperty(name = "商品数量")
        private Integer num;

        @ApiModelProperty("商品名称")
        private String goodName;

        @ApiModelProperty("商品sku明细")
        private String[] goodModel;

        @ApiModelProperty("商品图片")
        private String[] goodImg;

        @ApiModelProperty("sku图片")
        private String goodSkuImg;

        @ApiModelProperty("商品价格")
        private BigDecimal goodPrice;

        @ApiModelProperty("商品折扣单价")
        private BigDecimal goodDiscountPrice;

        @ApiModelProperty("商品库存")
        private Integer goodQuantity;

        @ApiModelProperty("是否是秒杀商品")
        private Boolean isFlashSale;

        @ApiModelProperty("秒杀价")
        private BigDecimal flashPurchasePrice;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderFeeDTO {

        /**
         * 配送费用
         */
        private BigDecimal deliveryFee;

        /**
         * 商品应付总价
         */
        private BigDecimal goodTotalPrice;

        /**
         * 优惠券抵扣金额
         */
        private BigDecimal couponDiscountAmount;

        /**
         * 税费用
         */
        private BigDecimal gstFee;

        /**
         * 最终应付总额 (goodTotalPrice + deliveryFee + gstFee)
         */
        private BigDecimal totalAmount;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserCouponDTO {

        /**
         * 配送费用
         */
        private BigDecimal deliveryFee;

        /**
         * 商品应付总价
         */
        private BigDecimal goodTotalPrice;

        /**
         * 优惠券抵扣金额
         */
        private BigDecimal couponDiscountAmount;

        /**
         * 税费用
         */
        private BigDecimal gstFee;

        /**
         * 最终应付总额 (goodTotalPrice + deliveryFee + gstFee)
         */
        private BigDecimal totalAmount;

    }
}

