package com.scash.app.domain.dto;

import com.scash.app.domain.Order;
import com.scash.app.domain.OrderGood;
import com.scash.app.domain.vo.CustomerVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Auther: TD_ZHX
 * @Date: 2021/3/29 10:31
 * @Description: 订单查询数据返回对象
 */
@Data
@ApiModel(value = "OrderQueryVo", description = "订单查询数据返回对象")
public class OrderQueryVo {

    // 订单对象
    @ApiModelProperty(value = "订单对象", position = 1)
    private Order order;

    // 订单编号
    @ApiModelProperty(hidden = true)
    private String orderNo;

    // 订单中的商品对象列表
    @ApiModelProperty(value = "订单中的商品对象列表", position = 1)
    private List<OrderGood> goodList;

    // 剩余秒数
    @ApiModelProperty(value = "剩余秒数", position = 1)
    private Integer timeLeft;

    @ApiModelProperty(value = "店铺Logo")
    private String storeLogo;

    @ApiModelProperty("用户信息")
    private CustomerVo customerVo;
}
