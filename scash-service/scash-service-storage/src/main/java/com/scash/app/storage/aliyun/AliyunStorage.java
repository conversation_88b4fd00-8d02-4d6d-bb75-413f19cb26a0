package com.scash.app.storage.aliyun;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import com.scash.app.storage.StorageApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 阿里云存储
 * <AUTHOR>
 */
@Slf4j
@Service("AliyunStorage")
public class AliyunStorage implements StorageApi {

    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    /**
     * 端点(地域节点)
     */
    @Value("${alipay.storage.endpoint}")
    public String endpoint;

    /**
     * 访问密钥Id
     */
    @Value("${alipay.storage.accessKeyId}")
    public String accessKeyId;

    /**
     * 访问密钥的秘密
     */
    @Value("${alipay.storage.accessKeySecret}")
    public String accessKeySecret;

    /**
     * oss bucket 名称
     */
    @Value("${alipay.storage.bucketName}")
    public String bucketName;

    /**
     * 获取阿里云OSS客户端对象
     */
    private OSSClient getOSSClient() {
        return new OSSClient(endpoint, accessKeyId, accessKeySecret);
    }

    private String getBaseUrl() {
        return "https://" + bucketName + "." + endpoint + "/" + sdf.format(new Date()) + "/";
    }

    /**
     * 阿里云OSS对象存储简单上传实现
     */
    @Override
    public void store(InputStream inputStream, long contentLength, String contentType, String keyName) {
        try {
            // 简单文件上传, 最大支持 5 GB, 适用于小文件上传, 建议 20M以下的文件使用该接口
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(contentLength);
            objectMetadata.setContentType(contentType);
            // 对象键（Key）是对象在存储桶中的唯一标识。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, sdf.format(new Date()) + "/" + keyName, inputStream, objectMetadata);
            PutObjectResult putObjectResult = getOSSClient().putObject(putObjectRequest);
            // logger.info("阿里云OSS对象存储上传返回结果：eTag:" + putObjectResult.getETag() + "requestId:" + putObjectResult.getRequestId() + "serverCRC:" + putObjectResult.getServerCRC());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

    }

    /**
     * 文件存储
     */
    @Override
    public void store(File file, String keyName) {
        try {
            // 对象键（Key）是对象在存储桶中的唯一标识。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, sdf.format(new Date()) + "/" + keyName, file);
            PutObjectResult putObjectResult = getOSSClient().putObject(putObjectRequest);
//			logger.info("阿里云OSS对象存储上传返回结果：eTag:" + putObjectResult.getETag() + "requestId:" + putObjectResult.getRequestId() + "serverCRC:" + putObjectResult.getServerCRC());
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }

    }

    /**
     * 删除
     */
    @Override
    public void delete(String keyName) {
        try {
            getOSSClient().deleteObject(bucketName, keyName);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    /**
     * 获取图片访问路径
     */
    @Override
    public String generateUrl(String keyName) {
        return getBaseUrl() + keyName;
    }
}
